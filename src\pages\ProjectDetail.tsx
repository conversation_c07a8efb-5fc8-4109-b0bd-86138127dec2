import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';

import { ArrowLeft, ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useProjects } from '@/contexts/ProjectContext';
import NavBar from '@/components/NavBar';
import Footer from '@/components/Footer';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";

function ProjectDetail() {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { getProjectById, getNextProject, getPreviousProject } = useProjects();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [carousel<PERSON>pi, setCarouselApi] = useState<any>(null);

  // Convert projectId to number
  const id = parseInt(projectId || '0', 10);

  // Get project data
  const project = getProjectById(id);
  const nextProject = getNextProject(id);
  const prevProject = getPreviousProject(id);

  // If project not found, redirect to homepage
  useEffect(() => {
    if (!project) {
      navigate('/');
    }
  }, [project, navigate]);

  // Scroll to top when projectId changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [projectId]);

  // Auto-scroll carousel every 8 seconds (slowed down from 5 seconds)
  useEffect(() => {
    if (!carouselApi) return;

    const interval = setInterval(() => {
      carouselApi.scrollNext();
    }, 8000);

    return () => clearInterval(interval);
  }, [carouselApi]);

  // Function to handle indicator clicks
  const handleIndicatorClick = (index: number) => {
    if (carouselApi) {
      carouselApi.scrollTo(index);
    }
  };

  if (!project) {
    return null; // Will redirect via useEffect
  }

  // Create an array of all carousel images
  const allImages = [
    project.imageUrl,
    ...(project.gallery
      ? project.gallery.filter(img => img !== project.imageUrl)
      : [])
  ];

  return (
    <div className="flex flex-col min-h-screen bg-ouroboros-background">
      {/* Full-width Image Carousel as hero section */}
      <div className="w-full relative">
        <NavBar />

        <Carousel
          className="w-full"
          opts={{
            loop: true,
            startIndex: 0,
            align: "center"
          }}
          setApi={(api) => {
            setCarouselApi(api);
            api?.on("select", () => {
              setCurrentSlide(api.selectedScrollSnap());
            });
          }}
        >
          <CarouselContent>
            {allImages.map((image, idx) => (
              <CarouselItem key={idx}>
                <div className="relative h-screen w-full overflow-hidden">
                  <img
                    src={image}
                    alt={`${project.title} afbeelding ${idx + 1}`}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                  />
                  {/* Subtle gradient only at bottom for text readability */}
                  <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-black/60 to-transparent" />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>

          {/* Navigation controls - White design */}
          <CarouselPrevious className="left-6 h-12 w-12 bg-white hover:bg-white/90 border-none text-ouroboros-accent rounded-full shadow-lg opacity-90 hover:opacity-100 transition-all duration-300">
            <ChevronLeft className="h-6 w-6" />
          </CarouselPrevious>
          <CarouselNext className="right-6 h-12 w-12 bg-white hover:bg-white/90 border-none text-ouroboros-accent rounded-full shadow-lg opacity-90 hover:opacity-100 transition-all duration-300">
            <ChevronRight className="h-6 w-6" />
          </CarouselNext>

          {/* Carousel indicators - White dots */}
          <div className="absolute bottom-6 left-0 right-0 flex justify-center gap-3 z-10">
            {allImages.map((_, idx) => (
              <button
                key={idx}
                className={`h-3 rounded-full transition-all shadow-md ${
                  currentSlide === idx
                    ? "bg-white w-10"
                    : "bg-white/50 w-3 hover:bg-white/80"
                }`}
                onClick={() => handleIndicatorClick(idx)}
                aria-label={`Go to slide ${idx + 1}`}
              />
            ))}
          </div>

          {/* Back to homepage navigation - White button */}
          <div className="absolute top-24 left-8 z-10">
            <Link
              to="/"
              className="inline-flex items-center text-ouroboros-accent hover:text-ouroboros-accent/80 transition-colors bg-white hover:bg-white/90 px-4 py-2 rounded-md shadow-md"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              <span className="font-medium">Terug naar projecten</span>
            </Link>
          </div>
        </Carousel>

        {/* Fixed project title overlay - positioned like content below */}
        <div className="absolute bottom-20 left-0 right-0 z-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="prose prose-lg max-w-none text-white">
                <h1 className="font-trajan text-3xl md:text-5xl mb-2 text-white">{project.title}</h1>
                <p className="text-lg md:text-xl max-w-2xl text-white/90">{project.tagline}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <main className="flex-grow">
        {/* Project content */}
        <div className="container mx-auto px-4 py-20">
          {/* Project content */}
          <div className="max-w-4xl mx-auto">

            {/* Project description */}
            <div className="prose prose-lg max-w-none text-ouroboros-accent/80 mb-16">
              <h2 className="font-trajan text-3xl text-ouroboros-accent mb-8">Synopsis</h2>
              <div className="leading-relaxed text-xl">
                {project.description.split('\n\n').map((paragraph, index) => (
                  <p key={index} className="mb-6">
                    {paragraph.split('\n').map((line, lineIndex) => (
                      <React.Fragment key={lineIndex}>
                        {line}
                        {lineIndex < paragraph.split('\n').length - 1 && <br />}
                      </React.Fragment>
                    ))}
                  </p>
                ))}
              </div>
            </div>

            {/* Credits - Same style as text above */}
            {project.people && project.people.length > 0 && (
              <div className="mt-16 mb-16">
                <h2 className="font-trajan text-3xl text-ouroboros-accent mb-8">Credits</h2>
                <div className="prose prose-lg max-w-none text-ouroboros-accent/80">
                  <div className="leading-relaxed text-xl space-y-3">
                    {project.people.map((person, idx) => (
                      <div key={idx} className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
                        <span className="font-medium text-ouroboros-accent">{person.name}</span>
                        <span className="text-ouroboros-accent/70 text-lg">{person.role}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Project navigation */}
            <div className="mt-16 mb-8 flex justify-between">
              {prevProject ? (
                <Link
                  to={`/projects/${prevProject.id}`}
                  className="flex items-center text-ouroboros-accent hover:text-ouroboros-accent/80 transition-colors group"
                >
                  <ChevronLeft className="mr-2 h-5 w-5 transition-transform group-hover:-translate-x-1" />
                  <div>
                    <span className="block text-sm text-ouroboros-accent/60">Vorig project</span>
                    <span className="font-medium">{prevProject.title}</span>
                  </div>
                </Link>
              ) : (
                <div></div>
              )}

              {nextProject ? (
                <Link
                  to={`/projects/${nextProject.id}`}
                  className="flex items-center text-ouroboros-accent hover:text-ouroboros-accent/80 transition-colors group text-right"
                >
                  <div>
                    <span className="block text-sm text-ouroboros-accent/60">Volgend project</span>
                    <span className="font-medium">{nextProject.title}</span>
                  </div>
                  <ChevronRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              ) : (
                <div></div>
              )}
            </div>

          </div>
        </div>
      </main>

      {/* Full-width CTA Section */}
      <section className="bg-ouroboros-accent py-16 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto flex flex-col md:flex-row items-center justify-between gap-8">
            <div className="text-left md:w-2/3">
              <h2 className="font-trajan text-3xl mb-4">Samenwerken?</h2>
              <p className="text-white/90 text-lg max-w-2xl">
                Wilt u meer weten over {project.title} of heeft u een idee voor uw eigen project?
                Neem contact met ons op om de mogelijkheden te bespreken.
              </p>
            </div>
            <div className="md:w-1/3 flex justify-center md:justify-end">
              <Button
                size="lg"
                className="bg-white text-ouroboros-accent hover:bg-white/90 group px-8 py-6 text-lg"
                onClick={() => {
                  navigate('/#contact');
                }}
              >
                <span>Neem contact op</span>
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

export default ProjectDetail;
